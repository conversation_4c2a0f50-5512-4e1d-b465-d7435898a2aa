# 수정할 부분 표시 (▲▼ 변경 지점 표시)

class BlogTemplateGenerator(QMainWindow):
    def __init__(self):
        super().__init__()
        # ...[기존 코드 유지]...
    
    # ▼ 중복된 auto_generate_content 메소드 제거
    def auto_generate_content(self):
        """AI가 주제를 바탕으로 고품질 블로그 콘텐츠를 생성합니다."""
        # ...[이 메소드 전체 삭제]...

    def auto_generate_content(self):  # ▲ 실제 사용되는 메소드 유지
        """AI가 주제를 바탕으로 자동으로 블로그 콘텐츠를 생성합니다."""
        # ...[기존 코드 유지]...

    def generate_html(self):
        if not self.sections:
            # ▼ 존재하지 않는 메소드 호출 제거
            # self.auto_generate_quality_content(title)  # 삭제
            pass
            
    # ▼ 미구현된 메소드 제거
    # def generate_high_quality_content(self, topic):  # 구현부 없음
    #     pass
    
    # ▼ 사용되지 않는 위젯 관련 코드 제거
    # def create_ai_bot_tab(self):  # 전체 탭 삭제 시
    #     # ...[전체 탭 구현 코드 삭제]...
    
    # def generate_ai_bot_html(self):  # 관련 메소드 삭제
    #     # ...[전체 메소드 삭제]...

    # ▼ 중복 애니메이션 스타일 제거
    def generate_html_template(self, ...):
        html_template = f'''<!DOCTYPE html>
        <style>
            /* ▼ 사용되지 않는 애니메이션 제거
            @keyframes pulse-purple {{
                0% {{ box-shadow: 0 0 0 0 rgba(155, 182, 155, 0.4); }}
                70% {{ box-shadow: 0 0 0 10px rgba(155, 182, 155, 0); }}
                100% {{ box-shadow: 0 0 0 0 rgba(155, 182, 155, 0); }}
            }}*/
        </style>
        '''