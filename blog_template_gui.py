import os
import sys
import webbrowser
from datetime import datetime
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTextEdit, QSpinBox,
                            QTabWidget, QGroupBox, QFormLayout, QCheckBox,
                            QMessageBox, QScrollArea, QComboBox, QFrame)
from PyQt5.QtGui import QFont

class BlogTemplateGenerator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("티스토리 블로그 템플릿 생성기")
        self.setMinimumSize(800, 600)

        # 메인 위젯 및 레이아웃 설정
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)

        # 탭 위젯 생성
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # 탭 생성
        self.create_basic_info_tab()
        self.create_sections_tab()
        self.create_coupang_tab()
        self.create_ai_bot_tab()
        self.create_preview_tab()

        # 하단 버튼 영역
        button_layout = QHBoxLayout()
        self.generate_button = QPushButton("HTML 생성하기")
        self.generate_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        self.generate_button.clicked.connect(self.generate_html)
        button_layout.addWidget(self.generate_button)

        main_layout.addLayout(button_layout)

        # 상태 표시줄
        self.statusBar().showMessage("블로그 템플릿 정보를 입력해주세요")

        # 섹션 데이터 저장용 리스트
        self.sections = []

    def create_basic_info_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 제목 영역
        title_group = QGroupBox("블로그 기본 정보")
        title_layout = QFormLayout()

        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("주제만 입력하세요 (예: 파이썬 기초, 다이어트 방법)")
        title_layout.addRow("블로그 주제:", self.title_input)

        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("자동 생성됩니다 (수정 가능)")
        title_layout.addRow("키워드:", self.keywords_input)

        # AI 자동 생성 버튼
        self.auto_generate_btn = QPushButton("🤖 AI로 자동 생성하기")
        self.auto_generate_btn.setStyleSheet("background-color: #FF6B6B; color: white; font-weight: bold; padding: 8px;")
        self.auto_generate_btn.clicked.connect(self.auto_generate_content)
        title_layout.addRow("", self.auto_generate_btn)

        title_group.setLayout(title_layout)
        layout.addWidget(title_group)

        # 스타일 선택 영역
        style_group = QGroupBox("블로그 스타일")
        style_layout = QFormLayout()

        self.color_theme = QComboBox()
        self.color_theme.addItems(["블루 테마", "핑크 테마", "그린 테마", "퍼플 테마"])
        style_layout.addRow("색상 테마:", self.color_theme)

        self.font_style = QComboBox()
        self.font_style.addItems(["기본 폰트", "나눔 고딕", "나눔 명조", "프리텐다드"])
        style_layout.addRow("폰트 스타일:", self.font_style)

        style_group.setLayout(style_layout)
        layout.addWidget(style_group)

        # 여백 추가
        layout.addStretch()

        self.tabs.addTab(tab, "기본 정보")

    def create_sections_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 섹션 컨트롤 영역
        control_layout = QHBoxLayout()

        self.section_count = QSpinBox()
        self.section_count.setRange(1, 10)
        self.section_count.setValue(3)
        control_layout.addWidget(QLabel("섹션 수:"))
        control_layout.addWidget(self.section_count)

        self.add_section_btn = QPushButton("섹션 추가")
        self.add_section_btn.clicked.connect(self.add_section_ui)
        control_layout.addWidget(self.add_section_btn)

        self.clear_sections_btn = QPushButton("모두 지우기")
        self.clear_sections_btn.clicked.connect(self.clear_sections)
        control_layout.addWidget(self.clear_sections_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 섹션 목록 영역
        self.sections_scroll = QScrollArea()
        self.sections_scroll.setWidgetResizable(True)
        self.sections_container = QWidget()
        self.sections_layout = QVBoxLayout(self.sections_container)
        self.sections_scroll.setWidget(self.sections_container)
        layout.addWidget(self.sections_scroll)

        # FAQ 추가 버튼
        self.add_faq_btn = QPushButton("FAQ 섹션 추가")
        self.add_faq_btn.clicked.connect(self.add_faq_section)
        layout.addWidget(self.add_faq_btn)

        self.tabs.addTab(tab, "섹션 관리")

    def create_coupang_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 쿠팡 파트너스 설명
        info_label = QLabel("쿠팡 파트너스 광고를 블로그에 삽입할 수 있습니다.")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 쿠팡 파트너스 ID 입력 영역
        coupang_group = QGroupBox("쿠팡 파트너스 설정")
        coupang_layout = QVBoxLayout()

        self.use_coupang = QCheckBox("쿠팡 파트너스 광고 사용")
        self.use_coupang.setChecked(True)
        coupang_layout.addWidget(self.use_coupang)

        form_layout = QFormLayout()

        # ID 입력 필드들
        self.coupang_ids = []
        for i in range(3):
            id_input = QLineEdit()
            id_input.setPlaceholderText(f"쿠팡 파트너스 ID {i+1} (기본값: 862597)")
            if i == 0:
                id_input.setText("862597")  # 첫 번째 필드에 기본값 설정
            form_layout.addRow(f"광고 ID {i+1}:", id_input)
            self.coupang_ids.append(id_input)

        coupang_layout.addLayout(form_layout)

        # 광고 위치 설정
        position_layout = QFormLayout()
        self.ad_position = QComboBox()
        self.ad_position.addItems(["상단", "중간", "하단", "모든 위치"])
        self.ad_position.setCurrentText("모든 위치")
        position_layout.addRow("광고 위치:", self.ad_position)

        coupang_layout.addLayout(position_layout)

        coupang_group.setLayout(coupang_layout)
        layout.addWidget(coupang_group)

        # 쿠팡 파트너스 가이드
        guide_group = QGroupBox("쿠팡 파트너스 가이드")
        guide_layout = QVBoxLayout()

        guide_text = QTextEdit()
        guide_text.setReadOnly(True)
        guide_text.setHtml("""
        <h3>쿠팡 파트너스 ID 찾는 방법</h3>
        <ol>
            <li>쿠팡 파트너스 사이트에 로그인합니다.</li>
            <li>'광고 관리' 메뉴로 이동합니다.</li>
            <li>'새 광고 만들기'를 클릭합니다.</li>
            <li>원하는 광고 유형을 선택하고 생성합니다.</li>
            <li>생성된 광고 코드에서 'id' 값을 찾아 입력합니다.</li>
        </ol>
        <p>예시: <code>new PartnersCoupang.G({"id":<b>862597</b>,"trackingCode":"*********"});</code></p>
        """)
        guide_layout.addWidget(guide_text)

        guide_group.setLayout(guide_layout)
        layout.addWidget(guide_group)

        layout.addStretch()
        self.tabs.addTab(tab, "쿠팡 파트너스")

    def create_ai_bot_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # AI 봇 설명
        info_label = QLabel("AI 봇을 블로그에 삽입하여 방문자와 상호작용할 수 있습니다.")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # AI 봇 설정 영역
        bot_group = QGroupBox("AI 봇 설정")
        bot_layout = QVBoxLayout()

        self.use_ai_bot = QCheckBox("AI 봇 사용")
        bot_layout.addWidget(self.use_ai_bot)

        # 봇 유형 선택
        form_layout = QFormLayout()
        self.bot_type = QComboBox()
        self.bot_type.addItems(["OpenAI GPT", "Claude", "Gemini", "커스텀 봇"])
        form_layout.addRow("봇 유형:", self.bot_type)

        # API 키 입력
        self.api_key = QLineEdit()
        self.api_key.setPlaceholderText("API 키를 입력하세요")
        self.api_key.setEchoMode(QLineEdit.Password)
        form_layout.addRow("API 키:", self.api_key)

        # 봇 이름 설정
        self.bot_name = QLineEdit()
        self.bot_name.setPlaceholderText("봇 이름 (예: 블로그 도우미)")
        form_layout.addRow("봇 이름:", self.bot_name)

        # 봇 프롬프트 설정
        self.bot_prompt = QTextEdit()
        self.bot_prompt.setPlaceholderText("봇에게 기본 지시사항을 입력하세요")
        self.bot_prompt.setMaximumHeight(100)
        form_layout.addRow("봇 지시사항:", self.bot_prompt)

        bot_layout.addLayout(form_layout)

        # 봇 위치 설정
        position_layout = QFormLayout()
        self.bot_position = QComboBox()
        self.bot_position.addItems(["우측 하단", "좌측 하단", "상단", "하단"])
        self.bot_position.setCurrentText("우측 하단")
        position_layout.addRow("봇 위치:", self.bot_position)

        bot_layout.addLayout(position_layout)

        bot_group.setLayout(bot_layout)
        layout.addWidget(bot_group)

        # 봇 스타일 설정
        style_group = QGroupBox("봇 스타일")
        style_layout = QFormLayout()

        self.bot_theme = QComboBox()
        self.bot_theme.addItems(["라이트 모드", "다크 모드", "블로그 테마에 맞춤"])
        style_layout.addRow("테마:", self.bot_theme)

        self.bot_size = QComboBox()
        self.bot_size.addItems(["작게", "중간", "크게"])
        self.bot_size.setCurrentText("중간")
        style_layout.addRow("크기:", self.bot_size)

        style_group.setLayout(style_layout)
        layout.addWidget(style_group)

        layout.addStretch()
        self.tabs.addTab(tab, "AI 봇")

    def create_preview_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        preview_label = QLabel("HTML 코드 미리보기")
        layout.addWidget(preview_label)

        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("HTML 생성 버튼을 클릭하면 여기에 코드가 표시됩니다.")
        layout.addWidget(self.preview_text)

        button_layout = QHBoxLayout()

        self.copy_button = QPushButton("코드 복사")
        self.copy_button.clicked.connect(self.copy_html)
        button_layout.addWidget(self.copy_button)

        self.save_button = QPushButton("HTML 파일 저장")
        self.save_button.clicked.connect(self.save_html)
        button_layout.addWidget(self.save_button)

        self.open_browser_button = QPushButton("브라우저에서 열기")
        self.open_browser_button.clicked.connect(self.open_in_browser)
        button_layout.addWidget(self.open_browser_button)

        layout.addLayout(button_layout)

        self.tabs.addTab(tab, "미리보기")

    def add_section_ui(self):
        section_frame = QFrame()
        section_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        section_frame.setStyleSheet("background-color: #f0f0f0; margin: 5px;")

        section_layout = QVBoxLayout(section_frame)

        # 섹션 헤더
        header_layout = QHBoxLayout()
        section_number = len(self.sections) + 1
        header_label = QLabel(f"섹션 {section_number}")
        header_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(header_label)

        # 삭제 버튼
        delete_btn = QPushButton("삭제")
        delete_btn.setMaximumWidth(60)
        delete_btn.clicked.connect(lambda: self.remove_section(section_frame))
        header_layout.addWidget(delete_btn)

        section_layout.addLayout(header_layout)

        # 섹션 내용 입력 폼
        form_layout = QFormLayout()

        title_input = QLineEdit()
        title_input.setPlaceholderText("섹션 제목")
        form_layout.addRow("제목:", title_input)

        emoji_input = QLineEdit()
        emoji_input.setPlaceholderText("이모지 (예: 📍)")
        emoji_input.setText("📍")
        form_layout.addRow("이모지:", emoji_input)

        content_input = QTextEdit()
        content_input.setPlaceholderText("섹션 내용을 입력하세요")
        content_input.setMaximumHeight(100)
        form_layout.addRow("내용:", content_input)

        # 추가 옵션
        options_layout = QHBoxLayout()

        add_list = QCheckBox("리스트 추가")
        options_layout.addWidget(add_list)

        add_emphasis = QCheckBox("강조 텍스트 추가")
        options_layout.addWidget(add_emphasis)

        add_highlight = QCheckBox("하이라이트 박스 추가")
        options_layout.addWidget(add_highlight)

        form_layout.addRow("옵션:", options_layout)

        section_layout.addLayout(form_layout)

        # 섹션 데이터 저장
        section_data = {
            "frame": section_frame,
            "title_input": title_input,
            "emoji_input": emoji_input,
            "content_input": content_input,
            "add_list": add_list,
            "add_emphasis": add_emphasis,
            "add_highlight": add_highlight,
            "list_items": [],
            "emphasis": {},
            "highlight": ""
        }

        self.sections.append(section_data)
        self.sections_layout.addWidget(section_frame)

    def remove_section(self, section_frame):
        for i, section in enumerate(self.sections):
            if section["frame"] == section_frame:
                self.sections.pop(i)
                section_frame.deleteLater()
                break

        # 섹션 번호 재정렬
        for i, section in enumerate(self.sections):
            header_label = section["frame"].layout().itemAt(0).layout().itemAt(0).widget()
            header_label.setText(f"섹션 {i+1}")

    def clear_sections(self):
        for section in self.sections:
            section["frame"].deleteLater()
        self.sections.clear()

    def add_faq_section(self):
        faq_frame = QFrame()
        faq_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        faq_frame.setStyleSheet("background-color: #e6f7ff; margin: 5px;")

        faq_layout = QVBoxLayout(faq_frame)

        # FAQ 헤더
        header_layout = QHBoxLayout()
        header_label = QLabel("FAQ 섹션")
        header_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(header_label)

        # 삭제 버튼
        delete_btn = QPushButton("삭제")
        delete_btn.setMaximumWidth(60)
        delete_btn.clicked.connect(lambda: self.remove_section(faq_frame))
        header_layout.addWidget(delete_btn)

        faq_layout.addLayout(header_layout)

        # FAQ 항목 관리
        faq_items_layout = QVBoxLayout()
        faq_items = []

        # FAQ 항목 추가 버튼
        add_faq_item_btn = QPushButton("FAQ 항목 추가")
        add_faq_item_btn.clicked.connect(lambda: self.add_faq_item(faq_items_layout, faq_items))
        faq_layout.addWidget(add_faq_item_btn)

        faq_layout.addLayout(faq_items_layout)

        # 섹션 데이터 저장
        section_data = {
            "frame": faq_frame,
            "title_input": QLineEdit("FAQ"),
            "emoji_input": QLineEdit("❓"),
            "content_input": QTextEdit("자주 묻는 질문과 답변입니다."),
            "add_list": QCheckBox(),
            "add_emphasis": QCheckBox(),
            "add_highlight": QCheckBox(),
            "list_items": [],
            "emphasis": {},
            "highlight": "",
            "faq": True,
            "faq_items": faq_items
        }

        self.sections.append(section_data)
        self.sections_layout.addWidget(faq_frame)

        # 기본 FAQ 항목 2개 추가
        self.add_faq_item(faq_items_layout, faq_items)
        self.add_faq_item(faq_items_layout, faq_items)

    def auto_generate_content(self):
        """AI가 주제를 바탕으로 고품질 블로그 콘텐츠를 생성합니다."""
        topic = self.title_input.text().strip()
        if not topic:
            QMessageBox.warning(self, "입력 오류", "먼저 블로그 주제를 입력해주세요.")
            return

        # 진행 상황 표시
        self.statusBar().showMessage("고품질 콘텐츠를 생성 중입니다... 잠시만 기다려주세요.")
        self.auto_generate_btn.setText("생성 중...")
        self.auto_generate_btn.setEnabled(False)

        try:
            # 기존 섹션 모두 삭제
            self.clear_sections()

            # 고품질 블로그 콘텐츠 생성
            blog_data = self.generate_high_quality_content(topic)

            # 제목과 키워드 설정
            self.title_input.setText(blog_data["title"])
            self.keywords_input.setText(", ".join(blog_data["keywords"]))

            # 섹션들 자동 생성
            for section_data in blog_data["sections"]:
                self.add_section_with_content(section_data)

            # FAQ 섹션 추가
            if blog_data.get("faq"):
                self.add_faq_section_with_content(blog_data["faq"])

            self.statusBar().showMessage("고품질 콘텐츠 생성 완료! 이제 HTML을 생성해보세요.")

        except Exception as e:
            QMessageBox.critical(self, "생성 오류", f"콘텐츠 생성 중 오류가 발생했습니다: {str(e)}")
            self.statusBar().showMessage("콘텐츠 생성에 실패했습니다.")

        finally:
            self.auto_generate_btn.setText("🤖 AI로 자동 생성하기")
            self.auto_generate_btn.setEnabled(True)

    def auto_generate_content(self):
        """AI가 주제를 바탕으로 자동으로 블로그 콘텐츠를 생성합니다."""
        topic = self.title_input.text().strip()
        if not topic:
            QMessageBox.warning(self, "입력 오류", "먼저 블로그 주제를 입력해주세요.")
            return

        # 진행 상황 표시
        self.statusBar().showMessage("AI가 콘텐츠를 생성 중입니다... 잠시만 기다려주세요.")
        self.auto_generate_btn.setText("생성 중...")
        self.auto_generate_btn.setEnabled(False)

        try:
            # 기존 섹션 모두 삭제
            self.clear_sections()

            # AI 콘텐츠 생성
            blog_data = self.generate_ai_content(topic)

            # 제목과 키워드 설정
            self.title_input.setText(blog_data["title"])
            self.keywords_input.setText(", ".join(blog_data["keywords"]))

            # 섹션들 자동 생성
            for section_data in blog_data["sections"]:
                self.add_section_with_content(section_data)

            # FAQ 섹션 추가
            if blog_data.get("faq"):
                self.add_faq_section_with_content(blog_data["faq"])

            self.statusBar().showMessage("AI 콘텐츠 생성이 완료되었습니다! 이제 HTML을 생성해보세요.")

        except Exception as e:
            QMessageBox.critical(self, "생성 오류", f"콘텐츠 생성 중 오류가 발생했습니다: {str(e)}")
            self.statusBar().showMessage("콘텐츠 생성에 실패했습니다.")

        finally:
            self.auto_generate_btn.setText("🤖 AI로 자동 생성하기")
            self.auto_generate_btn.setEnabled(True)

    def add_faq_item(self, parent_layout, faq_items):
        faq_item_frame = QFrame()
        faq_item_frame.setFrameStyle(QFrame.Box | QFrame.Sunken)
        faq_item_frame.setStyleSheet("background-color: white; margin: 2px;")

        faq_item_layout = QFormLayout(faq_item_frame)

        question_input = QLineEdit()
        question_input.setPlaceholderText("질문을 입력하세요")
        faq_item_layout.addRow("Q:", question_input)

        answer_input = QLineEdit()
        answer_input.setPlaceholderText("답변을 입력하세요")
        faq_item_layout.addRow("A:", answer_input)

        # 삭제 버튼
        delete_btn = QPushButton("삭제")
        delete_btn.setMaximumWidth(60)
        delete_btn.clicked.connect(lambda: self.remove_faq_item(faq_item_frame, faq_items))
        faq_item_layout.addRow("", delete_btn)

        parent_layout.addWidget(faq_item_frame)

        faq_items.append({
            "frame": faq_item_frame,
            "question": question_input,
            "answer": answer_input
        })

    def remove_faq_item(self, item_frame, faq_items):
        for i, item in enumerate(faq_items):
            if item["frame"] == item_frame:
                faq_items.pop(i)
                item_frame.deleteLater()
                break

    def generate_html(self):
        # 기본 정보 가져오기
        title = self.title_input.text()
        keywords_text = self.keywords_input.text()
        keywords = [k.strip() for k in keywords_text.split(",")]

        if not title:
            QMessageBox.warning(self, "입력 오류", "블로그 제목을 입력해주세요.")
            return

        # 섹션이 없으면 자동으로 고품질 콘텐츠 생성
        if not self.sections:
            self.auto_generate_quality_content(title)

        # 섹션 데이터 가져오기
        sections = []
        for section in self.sections:
            section_data = {
                "title": section["title_input"].text(),
                "emoji": section["emoji_input"].text(),
                "content": section["content_input"].toPlainText(),
                "add_list": section["add_list"].isChecked(),
                "add_emphasis": section["add_emphasis"].isChecked(),
                "add_highlight": section["add_highlight"].isChecked(),
                "list_items": section["list_items"],
                "emphasis": section["emphasis"],
                "highlight": section["highlight"]
            }
            sections.append(section_data)

        # FAQ 섹션 처리
        faq_sections = []
        regular_sections = []

        for section in sections:
            if section.get("faq", False):
                faq_sections.append(section)
            else:
                regular_sections.append(section)

        if faq_sections:
            faq_html = self.generate_faq_html(faq_sections[0])
        else:
            faq_html = ""

        sections = regular_sections

        # 쿠팡 파트너스 광고 HTML 생성
        coupang_html = self.generate_coupang_html() if self.use_coupang.isChecked() else ""

        # AI 챗봇 HTML 생성
        ai_bot_html = self.generate_ai_bot_html() if self.use_ai_bot.isChecked() else ""

        # 현재 날짜 가져오기
        current_date = datetime.now().strftime("%Y-%m-%d")

        # HTML 템플릿 생성
        html_template = self.generate_html_template(title, keywords, sections, faq_html, coupang_html, ai_bot_html, current_date)

        # 미리보기 텍스트 에디트에 HTML 코드 설정
        self.preview_text.setPlainText(html_template)

        # 상태 표시줄 업데이트
        self.statusBar().showMessage("HTML 코드가 생성되었습니다! 미리보기 탭에서 확인하세요.")

    def generate_faq_html(self, faq_section):
        faq_items = faq_section["faq_items"]
        faq_html = "<div class='faq-section'>\n"
        faq_html += f"  <h2 class='section-title'>{faq_section['title']}</h2>\n"
        faq_html += "  <div class='faq-container'>\n"

        for item in faq_items:
            question = item["question"].text()
            answer = item["answer"].text()
            faq_html += f"    <div class='faq-item'>\n"
            faq_html += f"      <div class='faq-question'>{question}</div>\n"
            faq_html += f"      <div class='faq-answer'>{answer}</div>\n"
            faq_html += "    </div>\n"

        faq_html += "  </div>\n"
        faq_html += "</div>\n"

        return faq_html

    def generate_coupang_html(self):
        coupang_ids = [id_input.text() for id_input in self.coupang_ids if id_input.text()]
        if not coupang_ids:
            return ""

        ad_position = self.ad_position.currentText()
        ad_positions = {
            "상단": "top",
            "중간": "middle",
            "하단": "bottom",
            "모든 위치": "all"
        }
        position = ad_positions.get(ad_position, "all")

        coupang_html = f"""
        <div class="coupang-ad" data-position="{position}">
          <script async src="https://www.coupang.com/partners/widgets/ad.js"></script>
          <script>
            PartnersCoupang.init({{
              id: [{','.join(coupang_ids)}],
              trackingCode: "*********"
            }});
          </script>
        </div>
        """

        return coupang_html

    def generate_ai_bot_html(self):
        bot_position = self.bot_position.currentText()
        bot_positions = {
            "우측 하단": "bottom-right",
            "좌측 하단": "bottom-left",
            "상단": "top",
            "하단": "bottom"
        }
        position = bot_positions.get(bot_position, "bottom-right")

        bot_theme = self.bot_theme.currentText()
        bot_themes = {
            "라이트 모드": "light",
            "다크 모드": "dark",
            "블로그 테마에 맞춤": "blog-theme"
        }
        theme = bot_themes.get(bot_theme, "light")

        bot_size = self.bot_size.currentText()
        bot_sizes = {
            "작게": "small",
            "중간": "medium",
            "크게": "large"
        }
        size = bot_sizes.get(bot_size, "medium")

        ai_bot_html = f"""
        <div class="ai-bot" data-position="{position}" data-theme="{theme}" data-size="{size}">
          <script async src="https://www.example.com/ai-bot.js"></script>
          <script>
            AIChatbot.init({{
              apiKey: "{self.api_key.text()}",
              botName: "{self.bot_name.text()}",
              prompt: "{self.bot_prompt.toPlainText()}"
            }});
          </script>
        </div>
        """

        return ai_bot_html

    def generate_html_template(self, title, keywords, sections, faq_html, coupang_html, ai_bot_html, current_date):
        toc_items = ""
        sections_html = ""

        for i, section in enumerate(sections):
            section_title = section["title"]
            toc_items += f"        <li><a href='#section-{i+1}'>{section_title}</a></li>\n"
            sections_html += f"      <div id='section-{i+1}' class='section'>\n"
            sections_html += f"        <h2 class='section-title'>{section_title}</h2>\n"
            sections_html += f"        <p class='section-content'>{section['content']}</p>\n"
            sections_html += "      </div>\n"

        keywords_meta = ', '.join(keywords) if keywords else ''

        html_template = f'''<!DOCTYPE html>
<html lang="ko">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="{keywords_meta}">
  <title>{title}</title>
  <style>
    body {{
      font-family: '나눔 고딕', sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }}

    .container {{
      max-width: 800px;
      margin: 20px auto;
      background-color: #fff;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }}

    .main-title {{
      font-size: 2.5rem;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
      animation: shake 1.5s infinite;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }}

    .toc-container {{
      background-color: #e0e0e0;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }}

    .toc-title {{
      font-size: 1.5rem;
      color: #333;
      margin-bottom: 10px;
    }}

    .toc-container ul {{
      list-style-type: none;
      padding: 0;
    }}

    .toc-container li {{
      margin: 5px 0;
    }}

    .toc-container a {{
      text-decoration: none;
      color: #007BFF;
    }}

    .toc-container a:hover {{
      text-decoration: underline;
    }}

    .section {{
      margin-bottom: 20px;
    }}

    .section-title {{
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 10px;
    }}

    .section-content {{
      font-size: 1.2rem;
      line-height: 1.6;
      color: #555;
    }}

    .faq-section {{
      margin-top: 20px;
    }}

    .faq-container {{
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 5px;
    }}

    .faq-item {{
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }}

    .faq-question {{
      font-weight: bold;
      margin-bottom: 5px;
    }}

    .faq-answer {{
      font-size: 1.1rem;
      color: #333;
    }}

    .coupang-ad {{
      margin-top: 20px;
      text-align: center;
    }}

    .ai-bot {{
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      width: 300px;
      height: 400px;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }}

    .emphasis-text {{
      font-size: 1.5rem;
      color: #ff6347;
      text-align: center;
      margin: 20px 0;
      animation: blink 1s infinite;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }}

    /* 애니메이션 */
    @keyframes shake {{
      0% {{ transform: translateX(0); }}
      25% {{ transform: translateX(-2px); }}
      50% {{ transform: translateX(0); }}
      75% {{ transform: translateX(2px); }}
      100% {{ transform: translateX(0); }}
    }}

    @keyframes blink {{
      0% {{ opacity: 1; }}
      50% {{ opacity: 0.8; }}
      100% {{ opacity: 1; }}
    }}

    @keyframes pulse-purple {{
      0% {{ box-shadow: 0 0 0 0 rgba(155, 89, 182, 0.4); }}
      70% {{ box-shadow: 0 0 0 10px rgba(155, 89, 182, 0); }}
      100% {{ box-shadow: 0 0 0 0 rgba(155, 89, 182, 0); }}
    }}

    /* 반응형 스타일 */
    @media (max-width: 768px) {{
      .main-title {{
        font-size: 1.8rem;
        padding: 20px 10px;
      }}

      .section-title {{
        font-size: 18px;
      }}

      .emphasis-text {{
        font-size: 18px;
      }}
    }}
  </style>
</head>
<body>
  <div class="container">
    <!-- 메인 타이틀 -->
    <h1 class="main-title">{title}</h1>

    <!-- 목차 -->
    <div class="toc-container">
      <h2 class="toc-title">📋 목차</h2>
      <ul>
{toc_items}
      </ul>
    </div>

    <!-- 본문 내용 -->
    <div class="content">
{sections_html}

{faq_html}

      <!-- 마무리 -->
      <p class="emphasis-text">
        <span class="neon-shake">📍 {title}, 이제 확실히 알겠죠?</span><br>
        👉 이 글이 도움이 되셨길 바랍니다!
      </p>

      <div class="final-cta" style="animation: pulse-purple 1.5s infinite;">
        <p>🧭 더 많은 정보가 필요하신가요?</p>
        <p style="margin-top: 10px;">아래 버튼을 클릭하여 관련 정보를 확인하세요!</p>
        <a href="#" class="button">더 알아보기</a>
      </div>

      <!-- 쿠팡 파트너스 광고 -->
{coupang_html}
    </div>
  </div>

  <!-- AI 챗봇 -->
{ai_bot_html}

  <!-- 작성일 표시 -->
  <div style="text-align: right; font-size: 12px; color: #888; margin: 30px 15px 10px;">
    작성일: {current_date}
  </div>
</body>
</html>'''

        return html_template

    def copy_html(self):
        """HTML 코드를 클립보드에 복사합니다."""
        html_code = self.preview_text.toPlainText()
        if html_code:
            clipboard = QApplication.clipboard()
            clipboard.setText(html_code)
            self.statusBar().showMessage("HTML 코드가 클립보드에 복사되었습니다!")
        else:
            QMessageBox.warning(self, "복사 오류", "복사할 HTML 코드가 없습니다. 먼저 HTML을 생성해주세요.")

    def save_html(self):
        """HTML 파일을 저장합니다."""
        html_code = self.preview_text.toPlainText()
        if not html_code:
            QMessageBox.warning(self, "저장 오류", "저장할 HTML 코드가 없습니다. 먼저 HTML을 생성해주세요.")
            return

        title = self.title_input.text()
        if not title:
            title = "블로그_템플릿"

        filename = f"{title.replace(' ', '_')}_블로그템플릿.html"

        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(html_code)

            self.statusBar().showMessage(f"HTML 파일이 성공적으로 저장되었습니다: {filename}")

            # 저장 성공 메시지
            QMessageBox.information(self, "저장 완료", f"HTML 파일이 성공적으로 저장되었습니다:\n{os.path.abspath(filename)}")
        except Exception as e:
            QMessageBox.critical(self, "저장 오류", f"파일 저장 중 오류가 발생했습니다: {str(e)}")

    def open_in_browser(self):
        """생성된 HTML을 브라우저에서 엽니다."""
        html_code = self.preview_text.toPlainText()
        if not html_code:
            QMessageBox.warning(self, "오류", "브라우저에서 열 HTML 코드가 없습니다. 먼저 HTML을 생성해주세요.")
            return

        # 임시 파일 생성
        title = self.title_input.text()
        if not title:
            title = "블로그_템플릿"

        filename = f"{title.replace(' ', '_')}_temp.html"

        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(html_code)

            # 브라우저에서 열기
            file_path = os.path.abspath(filename)
            webbrowser.open('file://' + file_path)

            self.statusBar().showMessage("HTML 파일이 브라우저에서 열렸습니다.")
        except Exception as e:
            QMessageBox.critical(self, "오류", f"브라우저에서 파일을 여는 중 오류가 발생했습니다: {str(e)}")

def main():
    app = QApplication(sys.argv)
    window = BlogTemplateGenerator()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
