(function() {
    "use strict";
    const s = "rtrvrai-label";
    function l(e, t) {
        let c;
        if (t) {
            const n = e.querySelector(`[${s}*="[id=${t}]"]`);
            c = (n == null ? void 0 : n.contentDocument) || (n == null ? void 0 : n.contentWindow.document)
        }
        return c || e
    }
    var a = (e => (e.Custom = "Custom",
    e.Search = "Search",
    e.Explore = "Explore",
    e.Research = "Research",
    e.Infer = "Infer",
    e))(a || {});
    Object.values(a);
    var o = (e => (e.PRO = "Gemini Pro",
    e.FLASH = "Gemini Flash",
    e.EXP = "Gemini Experimental",
    e.FLASH_LITE = "Gemini Flash Lite",
    e))(o || {});
    o.FLASH;
    var u = (e => (e.click_element = "click_element",
    e.type_into_element = "type_into_element",
    e.select_dropdown_value = "select_dropdown_value",
    e.type_and_enter = "type_and_enter",
    e.describe_image = "describe_image",
    e.scroll_page = "scroll_page",
    e.wait_action = "wait_action",
    e.go_back = "go_back",
    e.goto_url = "goto_url",
    e.google_search = "google_search",
    e.answer_task = "answer_task",
    e))(u || {});
    const g = new Set(Object.values(u));
    function b(e, t) {
        g.has(e.name) && e.args && typeof e.args.tab_id == "number" && (e.args.tab_id = t)
    }
    window.rtrvrAIExecuteSystemTool = ({tabIndex: e, call: t}) => {
        var d;
        const c = ((d = window.top) == null ? void 0 : d.document) || document
          , n = l(c, t.args.iframe_id)
          , _ = [];
        let r = {
            status: "Success"
        };
        switch (t.name) {
        case u.click_element:
            const i = n.querySelector(`[${s}*="[id=${t.args.element_id}]"]`);
            try {
                i ? (i.dispatchEvent(new MouseEvent("mousedown",{
                    bubbles: !0
                })),
                i.dispatchEvent(new MouseEvent("mouseup",{
                    bubbles: !0
                })),
                i.click(),
                r = {
                    status: "Success"
                }) : r = {
                    status: "Failure",
                    error: "Couldn't find the element"
                }
            } catch {
                r = {
                    status: "Failure",
                    error: "Exception executing click_element"
                }
            } finally {
                break
            }
        default:
            r = {
                status: "Failure",
                error: `Unknown tool name encountered: ${t.name}`
            };
            break
        }
        return b(t, e),
        _.push({
            name: t.name,
            args: t.args,
            response: r
        }),
        _
    }
}
)();
