import sys
import os
import webbrowser
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QTextEdit, QScrollArea, QFrame, QGroupBox, QComboBox)
from PyQt5.QtGui import QFont

class TistoryGenerator(QMainWindow):
    def __init__(self):
        super().__init__()
        # [기존 생성자 코드 유지...]
    
    def add_dynamic_section(self, preset=None):
        """동적 섹션 생성 시스템 (수정본)"""
        # [기존 코드 유지...]
        
        # 수정된 이벤트 연결
        move_up_btn.clicked.connect(lambda: self.move_section(section_frame, -1))
        move_down_btn.clicked.connect(lambda: self.move_section(section_frame, 1))
    
    # ▼ 추가된 메서드 ▼
    def move_section(self, section_frame, direction):
        """섹션 위치 이동 로직"""
        current_index = self.section_container_layout.indexOf(section_frame)
        new_index = current_index + direction
        
        if 0 <= new_index < self.section_container_layout.count():
            # 레이아웃에서 제거 후 재삽입
            self.section_container_layout.takeAt(current_index)
            self.section_container_layout.insertWidget(new_index, section_frame)
            
            # 리스트 순서 업데이트
            section = next(s for s in self.sections if s["frame"] is section_frame)
            self.sections.remove(section)
            self.sections.insert(new_index, section)
    
    def show_warning(self, message):
        """경고 메시지 표시"""
        self.statusBar().showMessage(message, 5000)
    
    def init_advanced_layout(self):
        """고급 모드 레이아웃 (임시 구현)"""
        self.input_panel = QWidget()
        self.input_layout = QVBoxLayout(self.input_panel)
        self.input_layout.addWidget(QLabel("고급 모드 준비 중..."))

# [나머지 코드는 동일...]