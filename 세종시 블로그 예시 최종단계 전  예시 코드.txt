<!DOCTYPE html>
<html lang="ko">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>세종의 강남, 어디일까?</title>
  
  <style>
    /* 기본 스타일 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Noto Sans KR', sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #fff;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0;
    }
    
    /* 헤더 스타일 */
    .main-title {
      background-color: #000;
      color: #fff;
      text-align: center;
      padding: 30px 10px;
      font-size: 2.2rem;
      font-weight: 700;
      margin-bottom: 0;
    }
    
    /* 목차 스타일 */
    .toc-container {
      background-color: #fff5f7;
      border: 2px solid #ff6b8b;
      border-radius: 0;
      padding: 15px;
      margin: 20px 15px;
    }
    
    .toc-title {
      text-align: center;
      margin-bottom: 15px;
      font-size: 1.3rem;
      font-weight: bold;
    }
    
    .toc-container ul {
      list-style: none;
      padding-left: 5px;
    }
    
    .toc-container li {
      margin-bottom: 8px;
      font-size: 0.95rem;
    }
    
    .toc-container a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
    }
    
    /* 본문 스타일 */
    .content {
      padding: 0 15px;
    }
    
    p {
      margin-bottom: 15px;
      font-size: 16px;
      line-height: 1.7;
    }
    
    /* 섹션 스타일 */
    .section-title {
      color: #000;
      font-size: 20px;
      font-weight: bold;
      background-color: #ffd8a8;
      padding: 10px;
      border-radius: 5px;
      margin: 30px 0 20px 0;
    }
    
    /* 하이라이트 박스 스타일 */
    .highlight-box {
      background-color: #e3fcec;
      border: 2px solid #27AE60;
      border-radius: 10px;
      padding: 15px;
      text-align: center;
      margin: 20px 0;
    }
    
    /* 강조 텍스트 스타일 */
    .emphasis-text {
      text-align: center;
      font-size: 22px;
      margin: 25px 0;
      animation: shake 1.2s infinite;
    }
    
    .neon-shake {
      color: #ff5733;
      font-weight: bold;
    }
    
    /* 테이블 스타일 */
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 14px;
    }
    
    .data-table th {
      background-color: #f2f2f2;
      padding: 10px;
      text-align: center;
      border: 1px solid #ddd;
    }
    
    .data-table td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: center;
    }
    
    /* FAQ 스타일 */
    .faq-title {
      color: #000;
      font-size: 20px;
      font-weight: bold;
      background-color: #ffd8a8;
      padding: 10px;
      border-radius: 5px;
      margin: 30px 0 20px 0;
    }
    
    .faq-question {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .faq-answer {
      margin-bottom: 15px;
    }
    
    /* 마무리 박스 스타일 */
    .final-cta {
      padding: 20px;
      border: 2px solid #9B59B6;
      background-color: #f5eeff;
      border-radius: 8px;
      text-align: center;
      margin: 30px 0;
    }
    
    .final-cta p:first-child {
      font-size: 18px;
      font-weight: bold;
      color: #8e44ad;
    }
    
    /* 버튼 스타일 */
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #ffd700;
      color: #000;
      text-decoration: none;
      border-radius: 8px;
      font-weight: bold;
      margin-top: 15px;
      border: 2px solid #000;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      animation: blink 1s infinite;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }
    
    /* 애니메이션 */
    @keyframes shake {
      0% { transform: translateX(0); }
      25% { transform: translateX(-2px); }
      50% { transform: translateX(0); }
      75% { transform: translateX(2px); }
      100% { transform: translateX(0); }
    }
    
    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.8; }
      100% { opacity: 1; }
    }
    
    @keyframes pulse-purple {
      0% { box-shadow: 0 0 0 0 rgba(155, 89, 182, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(155, 89, 182, 0); }
      100% { box-shadow: 0 0 0 0 rgba(155, 89, 182, 0); }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 메인 타이틀 -->
    <h1 class="main-title">세종의 강남, 어디일까?</h1>
    
    <div class="content">
      <!-- 목차 -->
      <div class="toc-container">
        <h2 class="toc-title">📋 목차</h2>
        <ul>
          <li><a href="#structure">📍 세종시의 도시 구조 이해하기</a></li>
          <li><a href="#center">🏙️ 세종의 중심, 나성동</a></li>
          <li><a href="#education">🏫 교육·문화의 강세 구역</a></li>
          <li><a href="#emerging">🚧 신흥 강남 후보지 분석</a></li>
          <li><a href="#compare">📊 강남 vs 비강남 생활권 비교</a></li>
          <li><a href="#value">💸 부동산 가치와 투자 매력</a></li>
          <li><a href="#map">🗺️ 세종시 강남 지도 한눈에 보기</a></li>
          <li><a href="#faq">❓ FAQ</a></li>
        </ul>
      </div>
      
      <p>세종시에 강남이 있을까요? 행정중심복합도시로 설계된 세종시는 서울처럼 구나 동으로 나뉘는 대신, '생활권'이라는 개념으로 구획되어 있어요.</p>
      
      <p>그중에서도 상업, 교육, 문화, 교통이 집약된 핵심 생활권이 바로 '세종의 강남'으로 불리고 있답니다. 오늘은 그 중심이 어디인지, 왜 그렇게 불리는지 깊이 있게 살펴볼게요. 🏙️</p>
      
      <p class="emphasis-text">
        <span class="neon-shake">🏙️ 세종의 중심, 어디일까요?</span><br>
        👉 다음 박스에서 <b>'나성동'</b> 집중 분석해드릴게요!
      </p>
      
      <!-- 다음 섹션 안내 -->
      <div class="highlight-box">
        🔍 지금부터 <b>2-4생활권 나성동</b>이 어떻게 '세종 강남'이라 불리게 되었는지 알려드릴게요!
      </div>
      
      <!-- 섹션 1 -->
      <h2 id="structure" class="section-title">📍 세종시의 도시 구조 이해하기</h2>
      
      <p>세종시는 행정중심복합도시로 계획된 도시로, 총 6개의 생활권으로 구분됩니다. 각 생활권은 고유한 특성과 개발 방향을 가지고 있으며, 이에 따라 부동산 가치도 차별화됩니다.</p>
      
      <p>세종시의 생활권은 크게 1~6생활권으로 나뉘며, 각 생활권 내에서도 세부적으로 구분됩니다. 예를 들어, 2생활권은 2-1, 2-2, 2-3, 2-4 등으로 세분화됩니다.</p>
      
      <p>특히 2-4생활권(나성동)은 세종시의 중심 상업지구로 발전하고 있으며, 다양한 편의시설과 상업시설이 밀집되어 있어 '세종의 강남'으로 불리고 있습니다.</p>
      
      <!-- 섹션 2 -->
      <h2 id="center" class="section-title">🏙️ 세종의 중심, 나성동</h2>
      
      <p>나성동(2-4생활권)은 세종시의 중심 상업지구로, 다양한 편의시설과 상업시설이 밀집되어 있습니다. 특히 프리미엄 아파트 단지와 고급 상업시설이 들어서면서 세종시의 '강남'으로 불리고 있습니다.</p>
      
      <p>나성동의 주요 특징:</p>
      <ul style="margin-left: 20px; margin-bottom: 15px;">
        <li>대형 상업시설 및 편의시설 밀집</li>
        <li>프리미엄 아파트 단지 위치</li>
        <li>교통 접근성 우수</li>
        <li>교육 인프라 확충 중</li>
      </ul>
      
      <p>나성동은 세종시의 핵심 생활권으로, 향후에도 지속적인 발전이 예상됩니다. 특히 상업시설과 교육 인프라가 확충되면서 부동산 가치도 상승할 것으로 전망됩니다.</p>
      
      <p class="emphasis-text">
        <span class="neon-shake">📈 미래 강남? 지금이 투자 타이밍일지도!</span><br>
        👉 다음은 생활권 간 <b>실제 비교 분석</b>으로 이어집니다!
      </p>
      
      <!-- 안내 박스 -->
      <div class="highlight-box">
        📊 다음에서는 <b>강남이라 불리는 지역과 일반 생활권의 비교</b>를 통해 차이점을 정리해드릴게요!
      </div>
      
      <!-- 섹션 3 -->
      <h2 id="education" class="section-title">🏫 교육·문화의 강세 구역</h2>
      
      <p>세종시에서 교육과 문화 인프라가 집중된 지역은 2-4 생활권과 1-5 생활권입니다. 특히 2-4 생활권은 국제학교와 특목고 유치가 예정되어 있어 교육 프리미엄이 형성될 것으로 예상됩니다.</p>
      
      <p>교육 환경이 우수한 지역은 부동산 가치도 높게 형성되는 경향이 있습니다. 세종시에서도 교육 인프라가 잘 갖춰진 지역을 중심으로 프리미엄이 형성되고 있습니다.</p>
      
      <!-- 섹션 4 -->
      <h2 id="emerging" class="section-title">🚧 신흥 강남 후보지 분석</h2>
      
      <p>세종시에서 '강남'으로 불릴 수 있는 지역은 현재 2-4 생활권이 유력하지만, 향후 개발 계획에 따라 다른 지역도 부상할 가능성이 있습니다.</p>
      
      <p>특히 3-3 생활권은 대학 캠퍼스와 연구시설이 들어설 예정이며, 젊은 인구 유입이 예상되어 장기적 관점에서 투자 가치가 있습니다.</p>
      
      <p>신흥 지역에 투자할 경우, 개발 계획의 변경 가능성과 인프라 구축 지연 리스크를 고려해야 합니다.</p>
      
      <!-- 섹션 5 -->
      <h2 id="compare" class="section-title">📊 강남 vs 비강남 생활권 비교</h2>
      
      <p>세종시 내에서 '강남'으로 불리는 지역과 그렇지 않은 지역 간에는 부동산 가격, 인프라, 교육 환경 등에서 차이가 있습니다.</p>
      
      <table class="data-table">
        <thead>
          <tr>
            <th>구분</th>
            <th>강남 지역(2-4 생활권)</th>
            <th>비강남 지역</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>아파트 가격</td>
            <td>높음</td>
            <td>상대적으로 낮음</td>
          </tr>
          <tr>
            <td>상업시설</td>
            <td>고급 상업시설 밀집</td>
            <td>기본 생활 편의시설 위주</td>
          </tr>
          <tr>
            <td>교육 환경</td>
            <td>영재학교, 특목고, 브랜드 초중고</td>
            <td>일반 공립학교 중심</td>
          </tr>
        </tbody>
      </table>
      
      <p>결국 본인의 목적이 '투자'인지 '실거주'인지에 따라 선택은 달라져요. 세종 강남권은 이미 자리 잡힌 프리미엄을 누릴 수 있고, 일반 생활권은 향후 성장 가능성에 투자하는 것이죠.</p>
      
      <p class="emphasis-text">
        <span class="neon-shake">📍 비교해보면 내 입지 전략이 보여요!</span><br>
        👉 다음은 <b>'세종 강남 지도 시각화'</b>로 이어집니다!
      </p>
      
      <!-- 안내 박스 -->
      <div class="highlight-box">
        🗺️ 다음에서는 <b>생활권별 강남 후보지 지도</b>로 한눈에 보기 쉽게 정리해드릴게요!
      </div>
      
      <!-- 섹션 6 -->
      <h2 id="value" class="section-title">💸 부동산 가치와 투자 매력</h2>
      
      <p>세종시 부동산 시장은 행정수도 이전 계획과 맞물려 지속적인 관심을 받고 있습니다. 특히 강남으로 불리는 2-4 생활권은 프리미엄이 형성되어 있어 안정적인 투자처로 평가받고 있습니다.</p>
      
      <p>세종시 부동산 투자는 장기적 관점에서 접근하는 것이 중요합니다. 단기 수익보다는 5-10년 후의 도시 발전 방향을 고려하여 투자하세요.</p>
      
      <!-- 섹션 7 -->
      <h2 id="map" class="section-title">🗺️ 세종시 강남 지도 한눈에 보기</h2>
      
      <p>세종시 강남 지역(2-4 생활권)은 지도상으로 세종시 중앙부에 위치하고 있으며, 주요 상업시설과 프리미엄 아파트 단지가 밀집해 있습니다.</p>
      
      <div style="text-align: center; margin: 20px 0;">
        <img src="https://via.placeholder.com/600x400" alt="세종시 강남 지도" style="max-width: 100%; border: 1px solid #ddd;">
      </div>
      
      <p>지도에서 볼 수 있듯이, 2-4 생활권은 주요 도로와 연결되어 있어 접근성이 뛰어나며, 향후 개발될 3생활권과도 인접해 있어 발전 가능성이 높습니다.</p>
      
      <div class="highlight-box">
        ✅ 마지막으로 <b>세종 강남 관련 실시간 궁금증 8가지</b>를 정리한 FAQ로 마무리해드릴게요!
      </div>
      
      <!-- 섹션 8 -->
      <h2 id="faq" class="section-title">❓ FAQ</h2>
      
      <p class="faq-question">Q1. 세종의 강남은 어디인가요?</p>
      <p class="faq-answer">A1. 일반적으로 나성동(2-4생활권), 어진동(2-1생활권), 도담동(1-5생활권)을 세종의 강남으로 불러요. 상업, 행정, 학군 중심지예요.</p>
      
      <p class="faq-question">Q2. 세종에서 아파트값이 가장 비싼 곳은?</p>
      <p class="faq-answer">A2. 현재 나성동(2-4생활권)과 도담동(1-5생활권)의 프리미엄 아파트 단지가 가장 높은 가격대를 형성하고 있습니다.</p>
      
      <p class="faq-question">Q3. 세종시 투자는 지금이 적기인가요?</p>
      <p class="faq-answer">A3. 세종시는 행정수도 이전 계획과 함께 장기적으로 발전 가능성이 높은 도시입니다. 단기 투자보다는 5-10년 이상의 장기 투자 관점에서 접근하는 것이 좋습니다.</p>
      
      <p class="faq-question">Q4. 세종시에서 교육 환경이 가장 좋은 곳은?</p>
      <p class="faq-answer">A4. 현재는 1-5생활권(도담동)이 교육 인프라가 잘 갖춰져 있으며, 향후 2-4생활권(나성동)에 국제학교와 특목고 유치가 예정되어 있어 교육 환경이 더욱 개선될 전망입니다.</p>
      
      <p class="faq-question">Q5. 세종시 신규 분양 아파트 중 추천 지역은?</p>
      <p class="faq-answer">A5. 현재 3생활권의 신규 분양 아파트는 상대적으로 저평가되어 있어 장기 투자 관점에서 매력적입니다. 특히 3-3생활권은 대학 캠퍼스와 연구시설 유치가 예정되어 있어 발전 가능성이 높습니다.</p>
      
      <p class="faq-question">Q6. 세종시 상가 투자는 어떤가요?</p>
      <p class="faq-answer">A6. 2-4생활권(나성동)의 상가는 이미 프리미엄이 형성되어 있어 진입 장벽이 높습니다. 3생활권의 상가는 아직 개발 초기 단계로 리스크가 있으나, 장기적으로는 수익성이 높을 수 있습니다.</p>
      
      <p class="faq-question">Q7. 세종시 부동산 시장의 향후 전망은?</p>
      <p class="faq-answer">A7. 행정수도 이전 계획이 구체화되면서 세종시 부동산 시장은 장기적으로 상승세를 유지할 것으로 전망됩니다. 특히 교통 인프라 확충과 상업시설 입점이 활발한 지역을 중심으로 가치 상승이 예상됩니다.</p>
      
      <p class="faq-question">Q8. 3-4생활권은 투자 가치가 없나요?</p>
      <p class="faq-answer">A8. 절대 아니에요! 3-4생활권은 개발 진행 중이고, GTX 연결과 기업 유치 등 호재도 많아 중장기 투자로 매우 유망해요.</p>
      
      <!-- 마무리 -->
      <p class="emphasis-text">
        <span class="neon-shake">📍 세종의 강남, 이제 확실히 알겠죠?</span><br>
        📌 목적에 맞는 생활권 선택이 가장 중요해요!
      </p>
      
      <div class="final-cta" style="animation: pulse-purple 1.5s infinite;">
        <p>🧭 생활권 맞춤 추천 원하시나요?</p>
        <p style="margin-top: 10px;">아래 버튼을 클릭하여 무료 상담을 신청하세요!</p>
        <a href="#" class="button">무료 상담 신청하기</a>
      </div>
      
      <!-- 쿠팡 파트너스 광고 -->
      <div style="margin: 40px 0; text-align: center;">
        <script src="https://ads-partners.coupang.com/g.js"></script>
        <script>
            new PartnersCoupang.G({"id":862603,"trackingCode":"AF8289089","subId":null,"template":"carousel","width":"680","height":"140"});
        </script>
      </div>
      
      <div style="margin: 20px 0; text-align: center;">
        <script src="https://ads-partners.coupang.com/g.js"></script>
        <script>
            new PartnersCoupang.G({"id":862600,"trackingCode":"AF8289089","subId":null,"template":"carousel","width":"680","height":"140"});
        </script>
      </div>
      
      <div style="margin: 20px 0; text-align: center;">
        <script src="https://ads-partners.coupang.com/g.js"></script>
        <script>
            new PartnersCoupang.G({"id":862597,"trackingCode":"AF8289089","subId":null,"template":"carousel","width":"680","height":"140"});
        </script>
      </div>
    </div>
  </div>
  
  <!-- 애니메이션 효과를 위한 추가 스타일 -->
  <style>
    /* 반응형 스타일 */
    @media screen and (max-width: 768px) {
      .main-title {
        font-size: 1.8rem;
        padding: 20px 10px;
      }
      
      .section-title {
        font-size: 18px;
      }
      
      .emphasis-text {
        font-size: 18px;
      }
      
      .toc-container {
        margin: 15px 10px;
      }
      
      .content {
        padding: 0 10px;
      }
      
      .data-table {
        font-size: 12px;
      }
      
      .data-table th, .data-table td {
        padding: 8px 5px;
      }
      
      .button {
        font-size: 14px;
        padding: 8px 15px;
      }
    }
    
    /* 깜빡임 효과 */
    .blink {
      animation: blink-animation 1s steps(5, start) infinite;
    }
    
    @keyframes blink-animation {
      to {
        visibility: hidden;
      }
    }
  </style>
</body>
</html>