#USE_CHAT_GPT_AI_ROOT {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  box-sizing: border-box;
  display: flex !important;
  flex-direction: column;
  opacity: 0;
  /* transition: all 0.1s ease-in-out; */
  z-index: -2147483600;
  visibility: hidden;
  width: unset;
  font-size: 14px !important;
}
#USE_CHAT_GPT_AI_ROOT_Minimize_Container {
  z-index: 2147483647;
  visibility: visible !important;
}
#USE_CHAT_GPT_AI_ROOT.open {
  min-width: 400px;
  opacity: 1;
  z-index: 2147483501;
}
#USE_CHAT_GPT_AI_ROOT.OVERLAY_CONTEXT_MENU {
  z-index: 2147483620;
}
#USE_CHAT_GPT_AI_ROOT.close {
  opacity: 0;
  display: none;
}

/* max-ai placeholder */
#max-ai-rich-text-editor-placeholder {
  user-select: none;
}
#max-ai-rich-text-editor-placeholder:before {
  content: attr(data-placeholder);
  position: absolute;
  top: 0;
  left: 0;
  padding-left: 2px;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  font-style: inherit;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  text-transform: inherit;
  text-decoration: inherit;
  text-align: inherit;
  text-indent: 0;
}
.maxai-snackbar-container-root {
  z-index: 2147483647 !important;
}
.maxai-snackbar-container-root div#notistack-snackbar {
  font-size: 14px;
}
/* max-ai placeholder end */

/*notion start*/
/*.notion-html [placeholder="Press ‘space’ for AI, ‘/’ for commands…"]:empty:after{*/
/*  content: " ";*/
/*}*/
/*notion end*/

/*gmail start */
.usechatgpt-ai__gmail-toolbar-button-group {
  transition: all 0.1s ease-in-out;
}
.usechatgpt-ai__gmail-toolbar-button-group:hover {
  box-shadow: 0 1px 2px 0 rgb(138 51 210 / 30%),
    0 1px 3px 1px rgb(138 51 210 / 30%);
  border-radius: 4px;
}
.usechatgpt-ai__gmail-toolbar-button-group:hover
  .usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown {
  background: #9134dc !important;
}

.usechatgpt-ai__gmail-toolbar-button-wrapper--cta {
  cursor: pointer;
  height: 36px !important;
  background: #9065b0 !important;
  padding: 0 8px !important;
  border-radius: 4px 0 0 4px !important;
  width: unset !important;
  transition: color 0.1s ease-in-out;
}
.usechatgpt-ai__gmail-toolbar-button-wrapper--cta:before {
  display: none !important;
}
.usechatgpt-ai__gmail-toolbar-button-wrapper--cta
  .usechatgpt-ai__gmail-toolbar-button--cta.inboxsdk__button_icon,
.usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown
  .usechatgpt-ai__gmail-toolbar-button--dropdown.inboxsdk__button_icon {
  opacity: 1;
}

.usechatgpt-ai__gmail-app-button.inboxsdk__button_icon {
  margin: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}
.usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown {
  cursor: pointer;
  height: 36px !important;
  background: #9065b0 !important;
  padding: 0 2px !important;
  border-radius: 0 4px 4px 0 !important;
  border-left: 1px solid rgba(0, 0, 0, 0.87) !important;
  margin-left: 0 !important;
  transition: color 0.1s ease-in-out;
}
.usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown.usechatgpt-ai__gmail-toolbar-button--dropdown:before {
  display: none !important;
}
.usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown.usechatgpt-ai__gmail-toolbar-button--dropdown {
  opacity: 1 !important;
}

.usechatgpt-ai__gmail-toolbar-button-wrapper--dropdown:hover,
.usechatgpt-ai__gmail-toolbar-button-wrapper--cta:hover {
  background: #9134dc !important;
}

body[dir='rtl'] #USE_CHAT_GPT_AI_ROOT {
  right: unset;
  left: 0;
}

.google-s-ad,
.google-s-ad__wrapper {
  display: none !important;
}

use-chat-gpt-ai.open,
#USE_CHAT_GPT_AI_ROOT.open,
use-chat-gpt-ai-content-menu {
  visibility: visible !important;
  outline: none;
}
#USE_CHAT_GPT_AI_ROOT_Wrapper {
  visibility: visible !important;
  outline: none;
}

/*gmail end*/

/* react pdf */
.hiddenCanvasElement {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  display: none;
}
/* react pdf */
