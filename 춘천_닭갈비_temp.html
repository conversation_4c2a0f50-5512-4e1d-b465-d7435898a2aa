<!DOCTYPE html>
<html lang="ko">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="keywords" content="#춘천 # 힐링 # 추억">
  <title>춘천 닭갈비</title>
  <style>
    body {
      font-family: '나눔 고딕', sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 800px;
      margin: 20px auto;
      background-color: #fff;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .main-title {
      font-size: 2.5rem;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
      animation: neon-shake 1.5s infinite;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .toc-container {
      background-color: #e0e0e0;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }

    .toc-title {
      font-size: 1.5rem;
      color: #333;
      margin-bottom: 10px;
    }

    .toc-container ul {
      list-style-type: none;
      padding: 0;
    }

    .toc-container li {
      margin: 5px 0;
    }

    .toc-container a {
      text-decoration: none;
      color: #007BFF;
    }

    .toc-container a:hover {
      text-decoration: underline;
    }

    .section {
      margin-bottom: 20px;
    }

    .section-title {
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 10px;
    }

    .section-content {
      font-size: 1.2rem;
      line-height: 1.6;
      color: #555;
    }

    .faq-section {
      margin-top: 20px;
    }

    .faq-container {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 5px;
    }

    .faq-item {
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .faq-question {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .faq-answer {
      font-size: 1.1rem;
      color: #333;
    }

    .coupang-ad {
      margin-top: 20px;
      text-align: center;
    }

    .ai-bot {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      width: 300px;
      height: 400px;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .emphasis-text {
      font-size: 1.5rem;
      color: #ff6347;
      text-align: center;
      margin: 20px 0;
      animation: blink 1s infinite;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* 애니메이션 */
    @keyframes shake {
      0% { transform: translateX(0); }
      25% { transform: translateX(-2px); }
      50% { transform: translateX(0); }
      75% { transform: translateX(2px); }
      100% { transform: translateX(0); }
    }

    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.8; }
      100% { opacity: 1; }
    }

    @keyframes pulse-purple {
      0% { box-shadow: 0 0 0 0 rgba(155, 89, 182, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(155, 89, 182, 0); }
      100% { box-shadow: 0 0 0 0 rgba(155, 89, 182, 0); }
    }

    /* 반응형 스타일 */
    @media (max-width: 768px) {
      .main-title {
        font-size: 1.8rem;
        padding: 20px 10px;
      }

      .section-title {
        font-size: 18px;
      }

      .emphasis-text {
        font-size: 18px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 메인 타이틀 -->
    <h1 class="main-title">춘천 닭갈비</h1>

    <!-- 목차 -->
    <div class="toc-container">
      <h2 class="toc-title">📋 목차</h2>
      <ul>
        <li><a href='#section-1'></a></li>

      </ul>
    </div>

    <!-- 본문 내용 -->
    <div class="content">
      <div id='section-1' class='section'>
        <h2 class='section-title'></h2>
        <p class='section-content'></p>
      </div>




      <!-- 마무리 -->
      <p class="emphasis-text">
        <span class="neon-shake">📍 춘천 닭갈비, 이제 확실히 알겠죠?</span><br>
        👉 이 글이 도움이 되셨길 바랍니다!
      </p>

      <div class="final-cta" style="animation: pulse-purple 1.5s infinite;">
        <p>🧭 더 많은 정보가 필요하신가요?</p>
        <p style="margin-top: 10px;">아래 버튼을 클릭하여 관련 정보를 확인하세요!</p>
        <a href="#" class="button">더 알아보기</a>
      </div>

      <!-- 쿠팡 파트너스 광고 -->

        <div class="coupang-ad" data-position="all">
          <script async src="https://www.coupang.com/partners/widgets/ad.js"></script>
          <script>
            PartnersCoupang.init({
              id: [862597],
              trackingCode: "AF8289089"
            });
          </script>
        </div>
        
    </div>
  </div>

  <!-- AI 챗봇 -->


  <!-- 작성일 표시 -->
  <div style="text-align: right; font-size: 12px; color: #888; margin: 30px 15px 10px;">
    작성일: 2025-05-26
  </div>
</body>
</html>