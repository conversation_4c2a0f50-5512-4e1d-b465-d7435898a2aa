!function(t, e) {
    "object" == typeof exports && "object" == typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define([], e) : "object" == typeof exports ? exports.PartnersCoupang = e() : t.PartnersCoupang = e()
}(self, (function() {
    return function() {
        var t = {
            3387: function(t) {
                t.exports = function(t) {
                    if ("function" != typeof t)
                        throw TypeError(t + " is not a function!");
                    return t
                }
            },
            4228: function(t, e, r) {
                var n = r(3305);
                t.exports = function(t) {
                    if (!n(t))
                        throw TypeError(t + " is not an object!");
                    return t
                }
            },
            6094: function(t) {
                var e = t.exports = {
                    version: "2.6.12"
                };
                "number" == typeof __e && (__e = e)
            },
            5052: function(t, e, r) {
                var n = r(3387);
                t.exports = function(t, e, r) {
                    if (n(t),
                    void 0 === e)
                        return t;
                    switch (r) {
                    case 1:
                        return function(r) {
                            return t.call(e, r)
                        }
                        ;
                    case 2:
                        return function(r, n) {
                            return t.call(e, r, n)
                        }
                        ;
                    case 3:
                        return function(r, n, o) {
                            return t.call(e, r, n, o)
                        }
                    }
                    return function() {
                        return t.apply(e, arguments)
                    }
                }
            },
            3344: function(t) {
                t.exports = function(t) {
                    if (null == t)
                        throw TypeError("Can't call method on  " + t);
                    return t
                }
            },
            1763: function(t, e, r) {
                t.exports = !r(9448)((function() {
                    return 7 != Object.defineProperty({}, "a", {
                        get: function() {
                            return 7
                        }
                    }).a
                }
                ))
            },
            6034: function(t, e, r) {
                var n = r(3305)
                  , o = r(7526).document
                  , i = n(o) && n(o.createElement);
                t.exports = function(t) {
                    return i ? o.createElement(t) : {}
                }
            },
            2127: function(t, e, r) {
                var n = r(7526)
                  , o = r(6094)
                  , i = r(3341)
                  , a = r(8859)
                  , c = r(5052)
                  , u = "prototype"
                  , f = function(t, e, r) {
                    var s, l, p, y, d = t & f.F, h = t & f.G, g = t & f.S, v = t & f.P, m = t & f.B, b = h ? n : g ? n[e] || (n[e] = {}) : (n[e] || {})[u], w = h ? o : o[e] || (o[e] = {}), x = w[u] || (w[u] = {});
                    for (s in h && (r = e),
                    r)
                        p = ((l = !d && b && void 0 !== b[s]) ? b : r)[s],
                        y = m && l ? c(p, n) : v && "function" == typeof p ? c(Function.call, p) : p,
                        b && a(b, s, p, t & f.U),
                        w[s] != p && i(w, s, y),
                        v && x[s] != p && (x[s] = p)
                };
                n.core = o,
                f.F = 1,
                f.G = 2,
                f.S = 4,
                f.P = 8,
                f.B = 16,
                f.W = 32,
                f.U = 64,
                f.R = 128,
                t.exports = f
            },
            9448: function(t) {
                t.exports = function(t) {
                    try {
                        return !!t()
                    } catch (t) {
                        return !0
                    }
                }
            },
            9461: function(t, e, r) {
                t.exports = r(4556)("native-function-to-string", Function.toString)
            },
            7526: function(t) {
                var e = t.exports = "undefined" != typeof window && window.Math == Math ? window : "undefined" != typeof self && self.Math == Math ? self : Function("return this")();
                "number" == typeof __g && (__g = e)
            },
            7917: function(t) {
                var e = {}.hasOwnProperty;
                t.exports = function(t, r) {
                    return e.call(t, r)
                }
            },
            3341: function(t, e, r) {
                var n = r(7967)
                  , o = r(1996);
                t.exports = r(1763) ? function(t, e, r) {
                    return n.f(t, e, o(1, r))
                }
                : function(t, e, r) {
                    return t[e] = r,
                    t
                }
            },
            2956: function(t, e, r) {
                t.exports = !r(1763) && !r(9448)((function() {
                    return 7 != Object.defineProperty(r(6034)("div"), "a", {
                        get: function() {
                            return 7
                        }
                    }).a
                }
                ))
            },
            3305: function(t) {
                t.exports = function(t) {
                    return "object" == typeof t ? null !== t : "function" == typeof t
                }
            },
            2750: function(t) {
                t.exports = !1
            },
            7967: function(t, e, r) {
                var n = r(4228)
                  , o = r(2956)
                  , i = r(3048)
                  , a = Object.defineProperty;
                e.f = r(1763) ? Object.defineProperty : function(t, e, r) {
                    if (n(t),
                    e = i(e, !0),
                    n(r),
                    o)
                        try {
                            return a(t, e, r)
                        } catch (t) {}
                    if ("get"in r || "set"in r)
                        throw TypeError("Accessors not supported!");
                    return "value"in r && (t[e] = r.value),
                    t
                }
            },
            1996: function(t) {
                t.exports = function(t, e) {
                    return {
                        enumerable: !(1 & t),
                        configurable: !(2 & t),
                        writable: !(4 & t),
                        value: e
                    }
                }
            },
            8859: function(t, e, r) {
                var n = r(7526)
                  , o = r(3341)
                  , i = r(7917)
                  , a = r(4415)("src")
                  , c = r(9461)
                  , u = "toString"
                  , f = ("" + c).split(u);
                r(6094).inspectSource = function(t) {
                    return c.call(t)
                }
                ,
                (t.exports = function(t, e, r, c) {
                    var u = "function" == typeof r;
                    u && (i(r, "name") || o(r, "name", e)),
                    t[e] !== r && (u && (i(r, a) || o(r, a, t[e] ? "" + t[e] : f.join(String(e)))),
                    t === n ? t[e] = r : c ? t[e] ? t[e] = r : o(t, e, r) : (delete t[e],
                    o(t, e, r)))
                }
                )(Function.prototype, u, (function() {
                    return "function" == typeof this && this[a] || c.call(this)
                }
                ))
            },
            4556: function(t, e, r) {
                var n = r(6094)
                  , o = r(7526)
                  , i = "__core-js_shared__"
                  , a = o[i] || (o[i] = {});
                (t.exports = function(t, e) {
                    return a[t] || (a[t] = void 0 !== e ? e : {})
                }
                )("versions", []).push({
                    version: n.version,
                    mode: r(2750) ? "pure" : "global",
                    copyright: "© 2020 Denis Pushkarev (zloirock.ru)"
                })
            },
            4472: function(t, e, r) {
                var n = r(1485)
                  , o = r(7926)
                  , i = r(3344);
                t.exports = function(t, e, r, a) {
                    var c = String(i(t))
                      , u = c.length
                      , f = void 0 === r ? " " : String(r)
                      , s = n(e);
                    if (s <= u || "" == f)
                        return c;
                    var l = s - u
                      , p = o.call(f, Math.ceil(l / f.length));
                    return p.length > l && (p = p.slice(0, l)),
                    a ? p + c : c + p
                }
            },
            7926: function(t, e, r) {
                "use strict";
                var n = r(7087)
                  , o = r(3344);
                t.exports = function(t) {
                    var e = String(o(this))
                      , r = ""
                      , i = n(t);
                    if (i < 0 || i == 1 / 0)
                        throw RangeError("Count can't be negative");
                    for (; i > 0; (i >>>= 1) && (e += e))
                        1 & i && (r += e);
                    return r
                }
            },
            7087: function(t) {
                var e = Math.ceil
                  , r = Math.floor;
                t.exports = function(t) {
                    return isNaN(t = +t) ? 0 : (t > 0 ? r : e)(t)
                }
            },
            1485: function(t, e, r) {
                var n = r(7087)
                  , o = Math.min;
                t.exports = function(t) {
                    return t > 0 ? o(n(t), 9007199254740991) : 0
                }
            },
            3048: function(t, e, r) {
                var n = r(3305);
                t.exports = function(t, e) {
                    if (!n(t))
                        return t;
                    var r, o;
                    if (e && "function" == typeof (r = t.toString) && !n(o = r.call(t)))
                        return o;
                    if ("function" == typeof (r = t.valueOf) && !n(o = r.call(t)))
                        return o;
                    if (!e && "function" == typeof (r = t.toString) && !n(o = r.call(t)))
                        return o;
                    throw TypeError("Can't convert object to primitive value")
                }
            },
            4415: function(t) {
                var e = 0
                  , r = Math.random();
                t.exports = function(t) {
                    return "Symbol(".concat(void 0 === t ? "" : t, ")_", (++e + r).toString(36))
                }
            },
            4514: function(t, e, r) {
                var n = r(7526).navigator;
                t.exports = n && n.userAgent || ""
            },
            5380: function(t, e, r) {
                "use strict";
                var n = r(2127)
                  , o = r(4472)
                  , i = r(4514)
                  , a = /Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);
                n(n.P + n.F * a, "String", {
                    padStart: function(t) {
                        return o(this, t, arguments.length > 1 ? arguments[1] : void 0, !0)
                    }
                })
            },
            9021: function(t, e, r) {
                var n;
                t.exports = (n = n || function(t, e) {
                    var n;
                    if ("undefined" != typeof window && window.crypto && (n = window.crypto),
                    "undefined" != typeof self && self.crypto && (n = self.crypto),
                    "undefined" != typeof globalThis && globalThis.crypto && (n = globalThis.crypto),
                    !n && "undefined" != typeof window && window.msCrypto && (n = window.msCrypto),
                    !n && void 0 !== r.g && r.g.crypto && (n = r.g.crypto),
                    !n)
                        try {
                            n = r(477)
                        } catch (t) {}
                    var o = function() {
                        if (n) {
                            if ("function" == typeof n.getRandomValues)
                                try {
                                    return n.getRandomValues(new Uint32Array(1))[0]
                                } catch (t) {}
                            if ("function" == typeof n.randomBytes)
                                try {
                                    return n.randomBytes(4).readInt32LE()
                                } catch (t) {}
                        }
                        throw new Error("Native crypto module could not be used to get secure random number.")
                    }
                      , i = Object.create || function() {
                        function t() {}
                        return function(e) {
                            var r;
                            return t.prototype = e,
                            r = new t,
                            t.prototype = null,
                            r
                        }
                    }()
                      , a = {}
                      , c = a.lib = {}
                      , u = c.Base = {
                        extend: function(t) {
                            var e = i(this);
                            return t && e.mixIn(t),
                            e.hasOwnProperty("init") && this.init !== e.init || (e.init = function() {
                                e.$super.init.apply(this, arguments)
                            }
                            ),
                            e.init.prototype = e,
                            e.$super = this,
                            e
                        },
                        create: function() {
                            var t = this.extend();
                            return t.init.apply(t, arguments),
                            t
                        },
                        init: function() {},
                        mixIn: function(t) {
                            for (var e in t)
                                t.hasOwnProperty(e) && (this[e] = t[e]);
                            t.hasOwnProperty("toString") && (this.toString = t.toString)
                        },
                        clone: function() {
                            return this.init.prototype.extend(this)
                        }
                    }
                      , f = c.WordArray = u.extend({
                        init: function(t, r) {
                            t = this.words = t || [],
                            this.sigBytes = r != e ? r : 4 * t.length
                        },
                        toString: function(t) {
                            return (t || l).stringify(this)
                        },
                        concat: function(t) {
                            var e = this.words
                              , r = t.words
                              , n = this.sigBytes
                              , o = t.sigBytes;
                            if (this.clamp(),
                            n % 4)
                                for (var i = 0; i < o; i++) {
                                    var a = r[i >>> 2] >>> 24 - i % 4 * 8 & 255;
                                    e[n + i >>> 2] |= a << 24 - (n + i) % 4 * 8
                                }
                            else
                                for (var c = 0; c < o; c += 4)
                                    e[n + c >>> 2] = r[c >>> 2];
                            return this.sigBytes += o,
                            this
                        },
                        clamp: function() {
                            var e = this.words
                              , r = this.sigBytes;
                            e[r >>> 2] &= 4294967295 << 32 - r % 4 * 8,
                            e.length = t.ceil(r / 4)
                        },
                        clone: function() {
                            var t = u.clone.call(this);
                            return t.words = this.words.slice(0),
                            t
                        },
                        random: function(t) {
                            for (var e = [], r = 0; r < t; r += 4)
                                e.push(o());
                            return new f.init(e,t)
                        }
                    })
                      , s = a.enc = {}
                      , l = s.Hex = {
                        stringify: function(t) {
                            for (var e = t.words, r = t.sigBytes, n = [], o = 0; o < r; o++) {
                                var i = e[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                                n.push((i >>> 4).toString(16)),
                                n.push((15 & i).toString(16))
                            }
                            return n.join("")
                        },
                        parse: function(t) {
                            for (var e = t.length, r = [], n = 0; n < e; n += 2)
                                r[n >>> 3] |= parseInt(t.substr(n, 2), 16) << 24 - n % 8 * 4;
                            return new f.init(r,e / 2)
                        }
                    }
                      , p = s.Latin1 = {
                        stringify: function(t) {
                            for (var e = t.words, r = t.sigBytes, n = [], o = 0; o < r; o++) {
                                var i = e[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                                n.push(String.fromCharCode(i))
                            }
                            return n.join("")
                        },
                        parse: function(t) {
                            for (var e = t.length, r = [], n = 0; n < e; n++)
                                r[n >>> 2] |= (255 & t.charCodeAt(n)) << 24 - n % 4 * 8;
                            return new f.init(r,e)
                        }
                    }
                      , y = s.Utf8 = {
                        stringify: function(t) {
                            try {
                                return decodeURIComponent(escape(p.stringify(t)))
                            } catch (t) {
                                throw new Error("Malformed UTF-8 data")
                            }
                        },
                        parse: function(t) {
                            return p.parse(unescape(encodeURIComponent(t)))
                        }
                    }
                      , d = c.BufferedBlockAlgorithm = u.extend({
                        reset: function() {
                            this._data = new f.init,
                            this._nDataBytes = 0
                        },
                        _append: function(t) {
                            "string" == typeof t && (t = y.parse(t)),
                            this._data.concat(t),
                            this._nDataBytes += t.sigBytes
                        },
                        _process: function(e) {
                            var r, n = this._data, o = n.words, i = n.sigBytes, a = this.blockSize, c = i / (4 * a), u = (c = e ? t.ceil(c) : t.max((0 | c) - this._minBufferSize, 0)) * a, s = t.min(4 * u, i);
                            if (u) {
                                for (var l = 0; l < u; l += a)
                                    this._doProcessBlock(o, l);
                                r = o.splice(0, u),
                                n.sigBytes -= s
                            }
                            return new f.init(r,s)
                        },
                        clone: function() {
                            var t = u.clone.call(this);
                            return t._data = this._data.clone(),
                            t
                        },
                        _minBufferSize: 0
                    })
                      , h = (c.Hasher = d.extend({
                        cfg: u.extend(),
                        init: function(t) {
                            this.cfg = this.cfg.extend(t),
                            this.reset()
                        },
                        reset: function() {
                            d.reset.call(this),
                            this._doReset()
                        },
                        update: function(t) {
                            return this._append(t),
                            this._process(),
                            this
                        },
                        finalize: function(t) {
                            return t && this._append(t),
                            this._doFinalize()
                        },
                        blockSize: 16,
                        _createHelper: function(t) {
                            return function(e, r) {
                                return new t.init(r).finalize(e)
                            }
                        },
                        _createHmacHelper: function(t) {
                            return function(e, r) {
                                return new h.HMAC.init(t,r).finalize(e)
                            }
                        }
                    }),
                    a.algo = {});
                    return a
                }(Math),
                n)
            },
            9956: function(t, e, r) {
                var n;
                t.exports = (n = r(9021),
                n.enc.Hex)
            },
            3009: function(t, e, r) {
                var n;
                t.exports = (n = r(9021),
                function(t) {
                    var e = n
                      , r = e.lib
                      , o = r.WordArray
                      , i = r.Hasher
                      , a = e.algo
                      , c = []
                      , u = [];
                    !function() {
                        function e(e) {
                            for (var r = t.sqrt(e), n = 2; n <= r; n++)
                                if (!(e % n))
                                    return !1;
                            return !0
                        }
                        function r(t) {
                            return 4294967296 * (t - (0 | t)) | 0
                        }
                        for (var n = 2, o = 0; o < 64; )
                            e(n) && (o < 8 && (c[o] = r(t.pow(n, .5))),
                            u[o] = r(t.pow(n, 1 / 3)),
                            o++),
                            n++
                    }();
                    var f = []
                      , s = a.SHA256 = i.extend({
                        _doReset: function() {
                            this._hash = new o.init(c.slice(0))
                        },
                        _doProcessBlock: function(t, e) {
                            for (var r = this._hash.words, n = r[0], o = r[1], i = r[2], a = r[3], c = r[4], s = r[5], l = r[6], p = r[7], y = 0; y < 64; y++) {
                                if (y < 16)
                                    f[y] = 0 | t[e + y];
                                else {
                                    var d = f[y - 15]
                                      , h = (d << 25 | d >>> 7) ^ (d << 14 | d >>> 18) ^ d >>> 3
                                      , g = f[y - 2]
                                      , v = (g << 15 | g >>> 17) ^ (g << 13 | g >>> 19) ^ g >>> 10;
                                    f[y] = h + f[y - 7] + v + f[y - 16]
                                }
                                var m = n & o ^ n & i ^ o & i
                                  , b = (n << 30 | n >>> 2) ^ (n << 19 | n >>> 13) ^ (n << 10 | n >>> 22)
                                  , w = p + ((c << 26 | c >>> 6) ^ (c << 21 | c >>> 11) ^ (c << 7 | c >>> 25)) + (c & s ^ ~c & l) + u[y] + f[y];
                                p = l,
                                l = s,
                                s = c,
                                c = a + w | 0,
                                a = i,
                                i = o,
                                o = n,
                                n = w + (b + m) | 0
                            }
                            r[0] = r[0] + n | 0,
                            r[1] = r[1] + o | 0,
                            r[2] = r[2] + i | 0,
                            r[3] = r[3] + a | 0,
                            r[4] = r[4] + c | 0,
                            r[5] = r[5] + s | 0,
                            r[6] = r[6] + l | 0,
                            r[7] = r[7] + p | 0
                        },
                        _doFinalize: function() {
                            var e = this._data
                              , r = e.words
                              , n = 8 * this._nDataBytes
                              , o = 8 * e.sigBytes;
                            return r[o >>> 5] |= 128 << 24 - o % 32,
                            r[14 + (o + 64 >>> 9 << 4)] = t.floor(n / 4294967296),
                            r[15 + (o + 64 >>> 9 << 4)] = n,
                            e.sigBytes = 4 * r.length,
                            this._process(),
                            this._hash
                        },
                        clone: function() {
                            var t = i.clone.call(this);
                            return t._hash = this._hash.clone(),
                            t
                        }
                    });
                    e.SHA256 = i._createHelper(s),
                    e.HmacSHA256 = i._createHmacHelper(s)
                }(Math),
                n.SHA256)
            },
            477: function() {}
        }
          , e = {};
        function r(n) {
            var o = e[n];
            if (void 0 !== o)
                return o.exports;
            var i = e[n] = {
                exports: {}
            };
            return t[n].call(i.exports, i, i.exports, r),
            i.exports
        }
        r.n = function(t) {
            var e = t && t.__esModule ? function() {
                return t.default
            }
            : function() {
                return t
            }
            ;
            return r.d(e, {
                a: e
            }),
            e
        }
        ,
        r.d = function(t, e) {
            for (var n in e)
                r.o(e, n) && !r.o(t, n) && Object.defineProperty(t, n, {
                    enumerable: !0,
                    get: e[n]
                })
        }
        ,
        r.g = function() {
            if ("object" == typeof globalThis)
                return globalThis;
            try {
                return this || new Function("return this")()
            } catch (t) {
                if ("object" == typeof window)
                    return window
            }
        }(),
        r.o = function(t, e) {
            return Object.prototype.hasOwnProperty.call(t, e)
        }
        ,
        r.r = function(t) {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {
                value: "Module"
            }),
            Object.defineProperty(t, "__esModule", {
                value: !0
            })
        }
        ;
        var n = {};
        return function() {
            "use strict";
            r.r(n),
            r.d(n, {
                G: function() {
                    return _
                },
                makePUID: function() {
                    return A
                }
            });
            r(5380);
            function t(t, r) {
                return function(t) {
                    if (Array.isArray(t))
                        return t
                }(t) || function(t, e) {
                    var r = null == t ? null : "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
                    if (null != r) {
                        var n, o, i, a, c = [], u = !0, f = !1;
                        try {
                            if (i = (r = r.call(t)).next,
                            0 === e) {
                                if (Object(r) !== r)
                                    return;
                                u = !1
                            } else
                                for (; !(u = (n = i.call(r)).done) && (c.push(n.value),
                                c.length !== e); u = !0)
                                    ;
                        } catch (t) {
                            f = !0,
                            o = t
                        } finally {
                            try {
                                if (!u && null != r.return && (a = r.return(),
                                Object(a) !== a))
                                    return
                            } finally {
                                if (f)
                                    throw o
                            }
                        }
                        return c
                    }
                }(t, r) || function(t, r) {
                    if (!t)
                        return;
                    if ("string" == typeof t)
                        return e(t, r);
                    var n = Object.prototype.toString.call(t).slice(8, -1);
                    "Object" === n && t.constructor && (n = t.constructor.name);
                    if ("Map" === n || "Set" === n)
                        return Array.from(t);
                    if ("Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
                        return e(t, r)
                }(t, r) || function() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }
            function e(t, e) {
                (null == e || e > t.length) && (e = t.length);
                for (var r = 0, n = new Array(e); r < e; r++)
                    n[r] = t[r];
                return n
            }
            var o = {
                parseParameters: function(e) {
                    var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "&"
                      , n = {};
                    if (e)
                        for (var o = e.split(r), i = 0; i < o.length; i++) {
                            var a = t(o[i].split("="), 2)
                              , c = a[0]
                              , u = a[1];
                            n[c] = decodeURIComponent(u)
                        }
                    return n
                },
                joinParameters: function(t) {
                    var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "&"
                      , r = [];
                    for (var n in t)
                        t.hasOwnProperty(n) && void 0 !== t[n] && null !== t[n] && r.push("".concat(n, "=").concat(encodeURIComponent(t[n])));
                    return r.join(e)
                },
                getQueryParameterMap: function() {
                    var t = location.search;
                    return t ? (t = t.slice(1),
                    o.parseParameters(t)) : {}
                },
                getHashParameterMap: function() {
                    var t = location.hash;
                    return t ? (t = t.slice(1),
                    o.parseParameters(t)) : {}
                },
                getReferrer: function() {
                    var t = document.referrer;
                    return function(t) {
                        return 0 === t.indexOf("data:")
                    }(t) || function(t) {
                        var e = !1;
                        return /^https?:\/\/(localhost|127.0.0.1)/.test(t) && (e = !0),
                        (t.indexOf("<\/script>") > -1 || t.indexOf("<script>") > -1) && (e = !0),
                        e
                    }(t) ? void 0 : t
                },
                getResolution: function() {
                    return "".concat(screen.width, "x").concat(screen.height)
                },
                generateUUID: function() {
                    if (window.crypto && "function" == typeof crypto.randomUUID)
                        return crypto.randomUUID();
                    var t = (new Date).getTime();
                    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                        var r = (t + 16 * Math.random()) % 16 | 0;
                        return t = Math.floor(t / 16),
                        ("x" == e ? r : 7 & r | 8).toString(16)
                    }
                    ))
                },
                ping: function(t, e) {
                    e && "function" == typeof navigator.sendBeacon ? navigator.sendBeacon(t) : (new Image).src = t
                },
                trim: function(t) {
                    return t.replace(/^\s+|\s+$/g, "")
                },
                getDepth: function() {
                    var t = 0;
                    try {
                        for (var e = self; e !== top; )
                            t++,
                            e = e.parent
                    } catch (e) {
                        t = -1
                    }
                    return t
                }
            }
              , i = o
              , a = ["WIDGET"];
            function c(t) {
                return c = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
                    return typeof t
                }
                : function(t) {
                    return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
                }
                ,
                c(t)
            }
            function u(t, e) {
                var r = Object.keys(t);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(t);
                    e && (n = n.filter((function(e) {
                        return Object.getOwnPropertyDescriptor(t, e).enumerable
                    }
                    ))),
                    r.push.apply(r, n)
                }
                return r
            }
            function f(t) {
                for (var e = 1; e < arguments.length; e++) {
                    var r = null != arguments[e] ? arguments[e] : {};
                    e % 2 ? u(Object(r), !0).forEach((function(e) {
                        s(t, e, r[e])
                    }
                    )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : u(Object(r)).forEach((function(e) {
                        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                    }
                    ))
                }
                return t
            }
            function s(t, e, r) {
                return (e = y(e))in t ? Object.defineProperty(t, e, {
                    value: r,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : t[e] = r,
                t
            }
            function l(t, e) {
                if (null == t)
                    return {};
                var r, n, o = function(t, e) {
                    if (null == t)
                        return {};
                    var r, n, o = {}, i = Object.keys(t);
                    for (n = 0; n < i.length; n++)
                        r = i[n],
                        e.indexOf(r) >= 0 || (o[r] = t[r]);
                    return o
                }(t, e);
                if (Object.getOwnPropertySymbols) {
                    var i = Object.getOwnPropertySymbols(t);
                    for (n = 0; n < i.length; n++)
                        r = i[n],
                        e.indexOf(r) >= 0 || Object.prototype.propertyIsEnumerable.call(t, r) && (o[r] = t[r])
                }
                return o
            }
            function p(t, e) {
                for (var r = 0; r < e.length; r++) {
                    var n = e[r];
                    n.enumerable = n.enumerable || !1,
                    n.configurable = !0,
                    "value"in n && (n.writable = !0),
                    Object.defineProperty(t, y(n.key), n)
                }
            }
            function y(t) {
                var e = function(t, e) {
                    if ("object" != c(t) || !t)
                        return t;
                    var r = t[Symbol.toPrimitive];
                    if (void 0 !== r) {
                        var n = r.call(t, e || "default");
                        if ("object" != c(n))
                            return n;
                        throw new TypeError("@@toPrimitive must return a primitive value.")
                    }
                    return ("string" === e ? String : Number)(t)
                }(t, "string");
                return "symbol" == c(e) ? e : String(e)
            }
            var d = function() {
                function t(e) {
                    var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "https://ads-partners.coupang.com/"
                      , n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "https://logs-partners.coupang.com/log/";
                    if (function(t, e) {
                        if (!(t instanceof e))
                            throw new TypeError("Cannot call a class as a function")
                    }(this, t),
                    !e.id)
                        throw new Error("instance id is required!");
                    var o = e.WIDGET
                      , c = void 0 === o ? "widgets.html" : o
                      , u = f(f({}, l(e, a)), {}, {
                        resolution: i.getResolution(),
                        depth: i.getDepth()
                    });
                    this.WIDGET = c,
                    this.baseParameters = u,
                    this.serverBaseUrl = r,
                    this.logServerBaseUrl = n
                }
                var e, r, n;
                return e = t,
                (r = [{
                    key: "getParameters",
                    value: function(t) {
                        return f(f(f({}, this.baseParameters), t), {}, {
                            serverBaseUrl: this.serverBaseUrl,
                            logServerBaseUrl: this.logServerBaseUrl
                        })
                    }
                }, {
                    key: "getAdServeUrl",
                    value: function(t) {
                        return "".concat(this.serverBaseUrl).concat(this.WIDGET, "?").concat(i.joinParameters(this.getParameters(t)))
                    }
                }, {
                    key: "getTrackingUrl",
                    value: function(t, e, r) {
                        var n = f(f({}, this.baseParameters), e)
                          , o = {};
                        return r && Array.isArray(r) ? r.forEach((function(t) {
                            o[t] = n[t]
                        }
                        )) : o = n,
                        "".concat(this.logServerBaseUrl).concat(t, "?").concat(i.joinParameters(o))
                    }
                }, {
                    key: "getEventTrackingUrl",
                    value: function(t) {
                        return this.getTrackingUrl("item-event", t)
                    }
                }, {
                    key: "getPersonalizationAdIconClickTrackingUrl",
                    value: function(t) {
                        var e = f(f(f({}, this.baseParameters), t), {}, {
                            platform: "web"
                        });
                        return "".concat(this.logServerBaseUrl, "icon-click?").concat(i.joinParameters(e))
                    }
                }]) && p(e.prototype, r),
                n && p(e, n),
                Object.defineProperty(e, "prototype", {
                    writable: !1
                }),
                t
            }();
            function h(t) {
                return h = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
                    return typeof t
                }
                : function(t) {
                    return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
                }
                ,
                h(t)
            }
            var g = ["onLoaded", "onDisplayed", "onClicked", "container", "serverBaseUrl", "logServerBaseUrl"];
            function v(t, e) {
                return function(t) {
                    if (Array.isArray(t))
                        return t
                }(t) || function(t, e) {
                    var r = null == t ? null : "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
                    if (null != r) {
                        var n, o, i, a, c = [], u = !0, f = !1;
                        try {
                            if (i = (r = r.call(t)).next,
                            0 === e) {
                                if (Object(r) !== r)
                                    return;
                                u = !1
                            } else
                                for (; !(u = (n = i.call(r)).done) && (c.push(n.value),
                                c.length !== e); u = !0)
                                    ;
                        } catch (t) {
                            f = !0,
                            o = t
                        } finally {
                            try {
                                if (!u && null != r.return && (a = r.return(),
                                Object(a) !== a))
                                    return
                            } finally {
                                if (f)
                                    throw o
                            }
                        }
                        return c
                    }
                }(t, e) || function(t, e) {
                    if (!t)
                        return;
                    if ("string" == typeof t)
                        return m(t, e);
                    var r = Object.prototype.toString.call(t).slice(8, -1);
                    "Object" === r && t.constructor && (r = t.constructor.name);
                    if ("Map" === r || "Set" === r)
                        return Array.from(t);
                    if ("Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
                        return m(t, e)
                }(t, e) || function() {
                    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
                }()
            }
            function m(t, e) {
                (null == e || e > t.length) && (e = t.length);
                for (var r = 0, n = new Array(e); r < e; r++)
                    n[r] = t[r];
                return n
            }
            function b(t, e) {
                var r = Object.keys(t);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(t);
                    e && (n = n.filter((function(e) {
                        return Object.getOwnPropertyDescriptor(t, e).enumerable
                    }
                    ))),
                    r.push.apply(r, n)
                }
                return r
            }
            function w(t) {
                for (var e = 1; e < arguments.length; e++) {
                    var r = null != arguments[e] ? arguments[e] : {};
                    e % 2 ? b(Object(r), !0).forEach((function(e) {
                        x(t, e, r[e])
                    }
                    )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : b(Object(r)).forEach((function(e) {
                        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                    }
                    ))
                }
                return t
            }
            function x(t, e, r) {
                return (e = P(e))in t ? Object.defineProperty(t, e, {
                    value: r,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : t[e] = r,
                t
            }
            function O(t, e) {
                if (null == t)
                    return {};
                var r, n, o = function(t, e) {
                    if (null == t)
                        return {};
                    var r, n, o = {}, i = Object.keys(t);
                    for (n = 0; n < i.length; n++)
                        r = i[n],
                        e.indexOf(r) >= 0 || (o[r] = t[r]);
                    return o
                }(t, e);
                if (Object.getOwnPropertySymbols) {
                    var i = Object.getOwnPropertySymbols(t);
                    for (n = 0; n < i.length; n++)
                        r = i[n],
                        e.indexOf(r) >= 0 || Object.prototype.propertyIsEnumerable.call(t, r) && (o[r] = t[r])
                }
                return o
            }
            function S(t, e) {
                for (var r = 0; r < e.length; r++) {
                    var n = e[r];
                    n.enumerable = n.enumerable || !1,
                    n.configurable = !0,
                    "value"in n && (n.writable = !0),
                    Object.defineProperty(t, P(n.key), n)
                }
            }
            function j(t, e, r) {
                return e && S(t.prototype, e),
                r && S(t, r),
                Object.defineProperty(t, "prototype", {
                    writable: !1
                }),
                t
            }
            function P(t) {
                var e = function(t, e) {
                    if ("object" != h(t) || !t)
                        return t;
                    var r = t[Symbol.toPrimitive];
                    if (void 0 !== r) {
                        var n = r.call(t, e || "default");
                        if ("object" != h(n))
                            return n;
                        throw new TypeError("@@toPrimitive must return a primitive value.")
                    }
                    return ("string" === e ? String : Number)(t)
                }(t, "string");
                return "symbol" == h(e) ? e : String(e)
            }
            var _ = j((function t(e) {
                !function(t, e) {
                    if (!(t instanceof e))
                        throw new TypeError("Cannot call a class as a function")
                }(this, t);
                var r = e.id
                  , n = "".concat(r, "-").concat(Math.random().toString(16).substr(2))
                  , o = w(w({}, e), {}, {
                    rUrl: i.getReferrer(),
                    tag: "js"
                })
                  , a = o.onLoaded
                  , c = o.onDisplayed
                  , u = o.onClicked
                  , f = o.container
                  , s = o.serverBaseUrl
                  , l = o.logServerBaseUrl
                  , p = O(o, g)
                  , y = new d(p,s,l)
                  , h = document.createElement("ins");
                if (h.style.display = "none",
                window.addEventListener("message", (function(t) {
                    try {
                        if ((t.origin.indexOf("coupangdev.com") > 0 || t.origin.indexOf("coupang.com") > 0) && t.data)
                            if ("string" == typeof t.data) {
                                var e = {};
                                try {
                                    e = JSON.parse(t.data)
                                } catch (t) {}
                                if ("set-iframe-size" === e.type && e.gid === n) {
                                    var o = document.getElementById("".concat(n));
                                    o && (o.width = e.width,
                                    o.height = e.height,
                                    h.style.display = "inline",
                                    t.source.postMessage(JSON.stringify({
                                        type: "iframe-size-has-been-set",
                                        gid: n
                                    }), t.origin))
                                }
                            } else
                                t.data.id == r && ("adLoaded" === t.data.type && "function" == typeof a ? a(t.data.hasAd) : "adDisplayed" === t.data.type && "function" == typeof c ? c() : "adClicked" === t.data.type && "function" == typeof u && u())
                    } catch (e) {
                        console.warn(e, t)
                    }
                }
                ), !1),
                f) {
                    if (f.nodeType || (f = document.querySelector(f)),
                    !f)
                        throw new Error("container is error!");
                    f.appendChild(h)
                } else {
                    var m = v([].slice.call(document.getElementsByTagName("script"), -1), 1)[0]
                      , b = m.parentNode;
                    b && b.insertBefore(h, m)
                }
                h.innerHTML = '<iframe id="'.concat(n, '" name="').concat(n, '" width="0" height="0" scrolling="no" frameborder="0" style="vertical-align:top" src="').concat(y.getAdServeUrl(), "#gid=").concat(n, '" referrerpolicy="unsafe-url" browsingtopics></iframe>')
            }
            ))
              , B = r(3009)
              , U = r.n(B)
              , E = r(9956)
              , T = r.n(E);
            function A(t, e) {
                return t && (t = t.replace(/^\s+|\s+$/gm, "")),
                t || (t = i.generateUUID()),
                U()(t + (e || "coupang")).toString(T())
            }
        }(),
        n
    }()
}
));
